/**
 * TerminalStatusMonitor - Real-time monitoring component for Square Terminal devices
 * Provides live status updates, connection monitoring, and health checks
 */

import { useState, useEffect, useCallback } from 'react'
import { supabaseAdmin } from '@/lib/supabase-admin'
import styles from '@/styles/admin/POS.module.css'

export default function TerminalStatusMonitor({ 
  onDeviceStatusChange, 
  showCompactView = false,
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}) {
  const [devices, setDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [lastUpdate, setLastUpdate] = useState(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Load devices and set up real-time monitoring
  useEffect(() => {
    loadDevices()
    
    if (autoRefresh) {
      const interval = setInterval(loadDevices, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // Set up real-time subscriptions for checkout updates
  useEffect(() => {
    const subscription = supabaseAdmin
      .from('terminal_checkout_updates')
      .on('INSERT', (payload) => {
        handleCheckoutUpdate(payload.new)
      })
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const loadDevices = useCallback(async () => {
    if (!loading) setIsRefreshing(true)
    
    try {
      const response = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        const newDevices = data.devices || []
        
        // Check for status changes
        if (onDeviceStatusChange && devices.length > 0) {
          newDevices.forEach(newDevice => {
            const oldDevice = devices.find(d => d.id === newDevice.id)
            if (oldDevice && oldDevice.status !== newDevice.status) {
              onDeviceStatusChange(newDevice, oldDevice.status)
            }
          })
        }
        
        setDevices(newDevices)
        setLastUpdate(new Date())
        setError('')
      } else {
        throw new Error('Failed to load device status')
      }
    } catch (err) {
      console.error('Error loading device status:', err)
      setError(err.message)
    } finally {
      setLoading(false)
      setIsRefreshing(false)
    }
  }, [devices, loading, onDeviceStatusChange])

  const handleCheckoutUpdate = (update) => {
    console.log('Terminal checkout update received:', update)
    // Trigger device refresh to get latest status
    loadDevices()
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PAIRED': return '🟢'
      case 'UNPAIRED': return '🔴'
      case 'UNKNOWN': return '🟡'
      default: return '⚪'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'PAIRED': return '#28a745'
      case 'UNPAIRED': return '#dc3545'
      case 'UNKNOWN': return '#ffc107'
      default: return '#6c757d'
    }
  }

  const formatLastUpdate = () => {
    if (!lastUpdate) return 'Never'
    
    const now = new Date()
    const diff = now - lastUpdate
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (seconds < 60) return `${seconds}s ago`
    if (minutes < 60) return `${minutes}m ago`
    return lastUpdate.toLocaleTimeString()
  }

  const getOverallStatus = () => {
    if (devices.length === 0) return { status: 'none', message: 'No devices configured' }
    
    const pairedCount = devices.filter(d => d.status === 'PAIRED').length
    const totalCount = devices.length
    
    if (pairedCount === 0) return { status: 'error', message: 'No devices paired' }
    if (pairedCount === totalCount) return { status: 'success', message: 'All devices paired' }
    return { status: 'warning', message: `${pairedCount}/${totalCount} devices paired` }
  }

  if (showCompactView) {
    const overall = getOverallStatus()
    
    return (
      <div className={styles.terminalStatusCompact}>
        <div className={styles.compactHeader}>
          <span className={styles.statusIndicator} style={{ color: getStatusColor(overall.status) }}>
            {overall.status === 'success' ? '🟢' : overall.status === 'warning' ? '🟡' : '🔴'}
          </span>
          <span className={styles.statusText}>{overall.message}</span>
          <button 
            className={styles.compactRefreshButton}
            onClick={loadDevices}
            disabled={isRefreshing}
            title="Refresh device status"
          >
            {isRefreshing ? '⟳' : '🔄'}
          </button>
        </div>
        
        {devices.length > 0 && (
          <div className={styles.compactDeviceList}>
            {devices.map((device) => (
              <div key={device.id} className={styles.compactDeviceItem}>
                <span className={styles.deviceStatusIcon}>
                  {getStatusIcon(device.status)}
                </span>
                <span className={styles.deviceNameShort}>
                  {device.name?.split(' ')[0] || `T${device.id.slice(-3)}`}
                </span>
              </div>
            ))}
          </div>
        )}
        
        <div className={styles.lastUpdateText}>
          Updated: {formatLastUpdate()}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.terminalStatusMonitor}>
      <div className={styles.monitorHeader}>
        <h4>Terminal Device Status</h4>
        <div className={styles.monitorControls}>
          <span className={styles.lastUpdateInfo}>
            Last updated: {formatLastUpdate()}
          </span>
          <button
            className={styles.refreshButton}
            onClick={loadDevices}
            disabled={isRefreshing}
          >
            {isRefreshing ? '⟳ Refreshing...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>⚠️</span>
          <span className={styles.errorText}>{error}</span>
          <button onClick={() => setError('')} className={styles.dismissError}>×</button>
        </div>
      )}

      {loading ? (
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading device status...</p>
        </div>
      ) : devices.length === 0 ? (
        <div className={styles.noDevicesState}>
          <div className={styles.noDevicesIcon}>📱</div>
          <h5>No Terminal Devices</h5>
          <p>No Square Terminal devices have been configured yet.</p>
        </div>
      ) : (
        <div className={styles.deviceStatusList}>
          {devices.map((device) => (
            <div key={device.id} className={styles.deviceStatusCard}>
              <div className={styles.deviceStatusHeader}>
                <div className={styles.deviceInfo}>
                  <span className={styles.deviceIcon}>📱</span>
                  <span className={styles.deviceName}>
                    {device.name || `Terminal ${device.id.slice(-4)}`}
                  </span>
                </div>
                <div 
                  className={styles.deviceStatus}
                  style={{ color: getStatusColor(device.status) }}
                >
                  {getStatusIcon(device.status)} {device.status}
                </div>
              </div>
              
              <div className={styles.deviceStatusDetails}>
                <div className={styles.statusDetail}>
                  <span className={styles.detailLabel}>Device ID:</span>
                  <span className={styles.detailValue}>
                    {device.deviceId || 'Not paired'}
                  </span>
                </div>
                
                <div className={styles.statusDetail}>
                  <span className={styles.detailLabel}>Code:</span>
                  <span className={styles.detailValue}>{device.code}</span>
                </div>
                
                {device.statusChangedAt && (
                  <div className={styles.statusDetail}>
                    <span className={styles.detailLabel}>Last Change:</span>
                    <span className={styles.detailValue}>
                      {new Date(device.statusChangedAt).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className={styles.monitorFooter}>
        <div className={styles.statusSummary}>
          <span>Total: {devices.length}</span>
          <span>Paired: {devices.filter(d => d.status === 'PAIRED').length}</span>
          <span>Unpaired: {devices.filter(d => d.status === 'UNPAIRED').length}</span>
        </div>
        
        <div className={styles.autoRefreshInfo}>
          {autoRefresh ? (
            <span>Auto-refresh: {refreshInterval / 1000}s</span>
          ) : (
            <span>Auto-refresh: Off</span>
          )}
        </div>
      </div>
    </div>
  )
}
