/**
 * POS Production Debugger
 *
 * This component provides real-time debugging specifically for POS terminal issues
 * in production mode, helping identify container initialization and auth recovery problems.
 */

import { useEffect, useState } from 'react';

const POSProductionDebugger = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [isVisible, setIsVisible] = useState(false);
  const [containerRetries, setContainerRetries] = useState(0);
  const [authRecoveryAttempts, setAuthRecoveryAttempts] = useState(0);

  // Monitor POS-specific issues
  useEffect(() => {
    const collectPOSDebugInfo = () => {
      const info = {
        timestamp: new Date().toISOString(),

        // Detect current POS context
        posContext: {
          // Main POS steps detection
          isOnServicesStep: !!document.querySelector('.serviceTileGrid') ||
                           !!document.querySelector('[class*="ServiceTileGrid"]'),
          isOnArtistsStep: !!document.querySelector('.artistSelector') ||
                          !!document.querySelector('[class*="ArtistSelector"]'),
          isOnTiersStep: !!document.querySelector('.serviceTierSelector') ||
                        !!document.querySelector('[class*="ServiceTierSelector"]'),
          isOnCheckoutStep: !!document.querySelector('.checkoutContent') ||
                           !!document.querySelector('[class*="checkoutContent"]'),

          // Checkout sub-steps detection
          isOnCustomerInfo: !!document.querySelector('.customerInfoForm') ||
                           !!document.querySelector('[class*="CustomerInfoForm"]'),
          isOnPaymentMethodSelection: !!document.querySelector('.paymentMethodSelector') ||
                                     !!document.querySelector('[class*="PaymentMethodSelector"]'),
          isOnCreditCardPayment: !!document.querySelector('.squarePaymentContainer') ||
                                !!document.querySelector('[class*="squarePaymentContainer"]'),

          // Payment processing state
          isProcessingCash: !!document.querySelector('.processingCash') ||
                           !!document.querySelector('[class*="processingCash"]')
        },

        // React Square Web Payments SDK status (only check when relevant)
        paymentForm: (() => {
          const isPaymentContext = !!document.querySelector('.squarePaymentContainer');

          if (!isPaymentContext) {
            return {
              contextRequired: false,
              status: 'not_required',
              reason: 'User not on credit card payment step'
            };
          }

          return {
            contextRequired: true,
            status: 'checking',

            // Check for React Square Web Payments SDK components
            hasPaymentForm: !!document.querySelector('[data-testid="payment-form"]') ||
                            !!document.querySelector('.sq-payment-form') ||
                            !!document.querySelector('[class*="PaymentForm"]'),

            hasCreditCard: !!document.querySelector('[data-testid="credit-card"]') ||
                           !!document.querySelector('.sq-card-iframe-container') ||
                           !!document.querySelector('[class*="CreditCard"]'),

            // Check for Square SDK iframes (React SDK creates these)
            hasSquareIframes: document.querySelectorAll('iframe[src*="square"]').length,

            // Payment form container status
            paymentContainer: !!document.querySelector('.squarePaymentContainer'),
            cardFormContainer: !!document.querySelector('.cardFormContainer'),

            // Processing state indicators
            hasProcessingOverlay: !!document.querySelector('.processingOverlay'),
            hasErrorDisplay: !!document.querySelector('.paymentError'),

            // Form dimensions and visibility
            formDimensions: (() => {
              const container = document.querySelector('.squarePaymentContainer');
              if (container) {
                const rect = container.getBoundingClientRect();
                return {
                  width: rect.width,
                  height: rect.height,
                  visible: rect.width > 0 && rect.height > 0,
                  top: rect.top,
                  left: rect.left
                };
              }
              return null;
            })()
          };
        })(),

        // Square SDK status (context-aware)
        square: (() => {
          const isPaymentContext = !!document.querySelector('.squarePaymentContainer');

          const baseInfo = {
            sdkLoaded: !!window.Square,
            sdkVersion: window.Square?.version || 'unknown',
            paymentsAvailable: !!(window.Square?.payments),

            // Environment configuration
            environment: {
              appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID ? 'SET' : 'MISSING',
              locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID ? 'SET' : 'MISSING',
              nodeEnv: process.env.NODE_ENV
            }
          };

          if (!isPaymentContext) {
            return {
              ...baseInfo,
              contextRequired: false,
              status: 'not_required',
              reason: 'User not on credit card payment step'
            };
          }

          return {
            ...baseInfo,
            contextRequired: true,
            status: 'checking',

            // React Square Web Payments SDK specific
            reactSDKLoaded: !!window.Square?.payments,
            canCreatePayments: (() => {
              try {
                if (!window.Square?.payments) return false;
                const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
                const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;
                return !!(appId && locationId);
              } catch (error) {
                return false;
              }
            })()
          };
        })(),

        // Payment processing status
        paymentProcessing: {
          // Check for active payment processing indicators
          isProcessing: !!document.querySelector('.processingOverlay'),
          hasActiveErrors: !!document.querySelector('.paymentError'),

          // Error categorization
          errorTypes: (() => {
            const errorElement = document.querySelector('.paymentError');
            if (errorElement) {
              const classList = Array.from(errorElement.classList);
              return {
                hasDeclineError: classList.some(cls => cls.includes('errorDecline')),
                hasNetworkError: classList.some(cls => cls.includes('errorNetwork')),
                hasValidationError: classList.some(cls => cls.includes('errorValidation')),
                hasSystemError: classList.some(cls => cls.includes('errorSystem'))
              };
            }
            return null;
          })(),

          // Payment form state
          formState: (() => {
            const cardContainer = document.querySelector('.cardFormContainer');
            const paymentForm = document.querySelector('.squarePaymentContainer');
            return {
              cardContainerExists: !!cardContainer,
              paymentFormExists: !!paymentForm,
              formVisible: cardContainer ? cardContainer.offsetHeight > 0 : false,
              hasSquareIframes: document.querySelectorAll('iframe[src*="square"]').length > 0
            };
          })()
        },

        // Terminal device status
        terminalDevices: (() => {
          const isTerminalContext = !!document.querySelector('.terminalPaymentContainer') ||
                                   !!document.querySelector('[class*="terminalPaymentContainer"]');

          if (!isTerminalContext) {
            return {
              contextRequired: false,
              status: 'not_required',
              reason: 'User not on terminal payment step'
            };
          }

          return {
            contextRequired: true,
            status: 'checking',
            hasDeviceManager: !!document.querySelector('.deviceManager'),
            hasDeviceSelection: !!document.querySelector('.deviceSelection'),
            hasProcessingState: !!document.querySelector('.processingState'),
            deviceCount: document.querySelectorAll('.deviceCard').length,
            selectedDevice: !!document.querySelector('.deviceCard.selected'),
            isProcessing: !!document.querySelector('.processingState'),
            hasTerminalInstructions: !!document.querySelector('.terminalInstructions')
          };
        })(),

        // POS protection status
        protection: {
          posOperationActive: sessionStorage.getItem('pos_operation_active') === 'true',
          posPaymentInProgress: sessionStorage.getItem('pos_payment_in_progress') === 'true',
          posSessionProtected: !!sessionStorage.getItem('pos_session_protected'),
          authRedirecting: sessionStorage.getItem('auth_redirecting') === 'true'
        },

        // Auth recovery status
        authRecovery: {
          recoverySystemLoaded: !!window.authWhiteScreenRecovery,
          isStuck: window.authWhiteScreenRecovery?.isStuck?.() || false,
          lastRecoveryAttempt: localStorage.getItem('last_auth_recovery_attempt')
        },

        // React hydration status
        hydration: {
          documentReady: document.readyState,
          bodyChildren: document.body.children.length,
          hasReactRoot: !!document.querySelector('#__next'),
          reactDevTools: !!(window.__REACT_DEVTOOLS_GLOBAL_HOOK__)
        },

        // Performance metrics
        performance: {
          navigationStart: performance.timing?.navigationStart,
          domContentLoaded: performance.timing?.domContentLoaded,
          loadComplete: performance.timing?.loadEventEnd,
          memoryUsage: performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
          } : null
        }
      };

      setDebugInfo(info);
    };

    collectPOSDebugInfo();

    // Update every 2 seconds
    const interval = setInterval(collectPOSDebugInfo, 2000);

    return () => clearInterval(interval);
  }, []);

  // Monitor console for payment processing messages
  useEffect(() => {
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    console.log = (...args) => {
      originalConsoleLog.apply(console, args);

      const message = args.join(' ');

      // Legacy container retry monitoring (for fallback compatibility)
      if (message.includes('Container ref not ready, retrying')) {
        setContainerRetries(prev => prev + 1);
        setIsVisible(true);
      }

      // Payment processing monitoring
      if (message.includes('Payment tokenized successfully') ||
          message.includes('Payment processed successfully') ||
          message.includes('Processing payment with token')) {
        // Payment flow is working - reset retries
        setContainerRetries(0);
      }

      if (message.includes('[Auth Recovery]')) {
        setAuthRecoveryAttempts(prev => prev + 1);
        setIsVisible(true);
      }
    };

    console.error = (...args) => {
      originalConsoleError.apply(console, args);

      const message = args.join(' ');

      // Monitor payment errors
      if (message.includes('Payment processing error') ||
          message.includes('Payment form error') ||
          message.includes('Square payment') ||
          message.includes('Payment API error')) {
        setIsVisible(true);
      }
    };

    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
    };
  }, []);

  // Auto-show debugger when issues are detected (context-aware)
  useEffect(() => {
    const shouldShow =
      containerRetries > 10 ||
      authRecoveryAttempts > 0 ||
      debugInfo.authRecovery?.isStuck ||
      // Only show Square SDK warnings when in payment context
      (debugInfo.square?.contextRequired && !debugInfo.square?.canCreatePayments) ||
      // Only show payment form warnings when in payment context
      (debugInfo.paymentForm?.contextRequired && !debugInfo.paymentForm?.hasSquareIframes) ||
      debugInfo.paymentProcessing?.hasActiveErrors;

    if (shouldShow) {
      setIsVisible(true);
    }
  }, [containerRetries, authRecoveryAttempts, debugInfo]);

  // Force payment form refresh
  const forcePaymentFormRefresh = () => {
    console.log('🔧 Forcing payment form refresh...');

    // For React Square Web Payments SDK, we can trigger a re-render
    // by dispatching a custom event that components can listen to
    const event = new CustomEvent('forceSquareRefresh', {
      detail: {
        source: 'POSProductionDebugger',
        timestamp: Date.now(),
        action: 'refresh_payment_form'
      }
    });
    window.dispatchEvent(event);

    // Also try the legacy event for backward compatibility
    const legacyEvent = new CustomEvent('forceSquareInit', {
      detail: { source: 'POSProductionDebugger' }
    });
    window.dispatchEvent(legacyEvent);
  };

  // Clear all POS protection flags
  const clearPOSProtection = () => {
    console.log('🧹 Clearing POS protection flags...');

    sessionStorage.removeItem('pos_operation_active');
    sessionStorage.removeItem('pos_payment_in_progress');
    sessionStorage.removeItem('pos_session_protected');
    sessionStorage.removeItem('auth_redirecting');

    setDebugInfo(prev => ({
      ...prev,
      protection: {
        posOperationActive: false,
        posPaymentInProgress: false,
        posSessionProtected: false,
        authRedirecting: false
      }
    }));
  };

  // Reset counters
  const resetCounters = () => {
    setContainerRetries(0);
    setAuthRecoveryAttempts(0);
  };

  // Test auth recovery protection
  const testAuthRecoveryProtection = () => {
    console.log('🧪 Testing auth recovery protection...');

    if (window.authWhiteScreenRecovery) {
      const isStuck = window.authWhiteScreenRecovery.isStuck();
      console.log('Auth recovery stuck check result:', isStuck);

      if (isStuck) {
        console.warn('⚠️ Auth recovery thinks we are stuck!');
      } else {
        console.log('✅ Auth recovery protection is working');
      }
    } else {
      console.warn('Auth recovery system not loaded');
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '60px',
          right: '10px',
          backgroundColor: containerRetries > 10 || authRecoveryAttempts > 0 ? '#ff4444' : '#333',
          color: '#fff',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          fontSize: '12px',
          cursor: 'pointer',
          zIndex: 9999
        }}
      >
        🏪 POS Debug ({
          // Only show SDK issues when in payment context
          (debugInfo.square?.contextRequired && !debugInfo.square?.canCreatePayments) ? 'SDK Issue' :
          debugInfo.paymentProcessing?.hasActiveErrors ? 'Payment Error' :
          containerRetries > 0 ? `${containerRetries} retries` :
          debugInfo.posContext?.isOnCreditCardPayment ? 'Payment Ready' :
          'OK'
        })
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      width: '400px',
      maxHeight: '500px',
      backgroundColor: '#1a1a1a',
      color: '#fff',
      border: '1px solid #444',
      borderRadius: '4px',
      padding: '15px',
      fontSize: '11px',
      fontFamily: 'monospace',
      zIndex: 10000,
      overflow: 'auto',
      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '15px',
        borderBottom: '1px solid #444',
        paddingBottom: '10px'
      }}>
        <strong style={{ color: '#4CAF50' }}>🏪 POS Production Debugger</strong>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ×
        </button>
      </div>

      {/* Status Overview */}
      <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
        {/* Current POS Context */}
        <div style={{ color: '#87CEEB', marginBottom: '8px', fontSize: '12px' }}>
          <strong>Current Step: {
            debugInfo.posContext?.isOnServicesStep ? 'Service Selection' :
            debugInfo.posContext?.isOnArtistsStep ? 'Artist Selection' :
            debugInfo.posContext?.isOnTiersStep ? 'Tier Selection' :
            debugInfo.posContext?.isOnCustomerInfo ? 'Customer Info' :
            debugInfo.posContext?.isOnPaymentMethodSelection ? 'Payment Method' :
            debugInfo.posContext?.isOnCreditCardPayment ? 'Credit Card Payment' :
            debugInfo.terminalDevices?.contextRequired ? 'Terminal Payment' :
            debugInfo.posContext?.isProcessingCash ? 'Processing Cash' :
            'Unknown'
          }</strong>
        </div>

        {/* Square SDK Status - Context Aware */}
        <div style={{
          color: debugInfo.square?.contextRequired ?
            (debugInfo.square?.canCreatePayments ? '#4CAF50' : '#ff6666') :
            '#999999',
          marginBottom: '5px'
        }}>
          <strong>Square SDK: {
            debugInfo.square?.contextRequired ?
              (debugInfo.square?.canCreatePayments ? 'READY' : 'NOT READY') :
              'N/A'
          }</strong>
          {debugInfo.square?.contextRequired && !debugInfo.square?.canCreatePayments && ' ⚠️ CHECK CONFIG'}
          {!debugInfo.square?.contextRequired && ' (Not required yet)'}
        </div>

        {/* Payment Form Status - Context Aware */}
        <div style={{
          color: debugInfo.paymentForm?.contextRequired ?
            (debugInfo.paymentForm?.hasSquareIframes ? '#4CAF50' : '#ff6666') :
            '#999999',
          marginBottom: '5px'
        }}>
          <strong>Payment Form: {
            debugInfo.paymentForm?.contextRequired ?
              (debugInfo.paymentForm?.hasSquareIframes ? 'LOADED' : 'NOT LOADED') :
              'N/A'
          }</strong>
          {debugInfo.paymentForm?.contextRequired && !debugInfo.paymentForm?.hasSquareIframes && ' ⚠️ NO IFRAMES'}
          {!debugInfo.paymentForm?.contextRequired && ' (Not required yet)'}
        </div>

        {/* Terminal Device Status - Context Aware */}
        <div style={{
          color: debugInfo.terminalDevices?.contextRequired ?
            (debugInfo.terminalDevices?.deviceCount > 0 ? '#4CAF50' : '#ff6666') :
            '#999999',
          marginBottom: '5px'
        }}>
          <strong>Terminal Devices: {
            debugInfo.terminalDevices?.contextRequired ?
              (debugInfo.terminalDevices?.deviceCount > 0 ?
                `${debugInfo.terminalDevices.deviceCount} AVAILABLE` : 'NO DEVICES') :
              'N/A'
          }</strong>
          {debugInfo.terminalDevices?.contextRequired && debugInfo.terminalDevices?.deviceCount === 0 && ' ⚠️ PAIR DEVICE'}
          {debugInfo.terminalDevices?.contextRequired && debugInfo.terminalDevices?.isProcessing && ' 🔄 PROCESSING'}
          {!debugInfo.terminalDevices?.contextRequired && ' (Not required yet)'}
        </div>

        {/* Processing Status */}
        <div style={{ color: debugInfo.paymentProcessing?.isProcessing ? '#ffa500' : (debugInfo.paymentProcessing?.hasActiveErrors ? '#ff6666' : '#4CAF50'), marginBottom: '5px' }}>
          <strong>Processing Status: {
            debugInfo.paymentProcessing?.isProcessing ? 'PROCESSING' :
            debugInfo.paymentProcessing?.hasActiveErrors ? 'ERROR' : 'READY'
          }</strong>
          {debugInfo.paymentProcessing?.isProcessing && ' 🔄 IN PROGRESS'}
          {debugInfo.paymentProcessing?.hasActiveErrors && ' ❌ HAS ERRORS'}
        </div>

        {/* Auth Recovery */}
        <div style={{ color: authRecoveryAttempts > 0 ? '#ff6666' : '#4CAF50' }}>
          <strong>Auth Recovery: {authRecoveryAttempts > 0 ? 'TRIGGERED' : 'STABLE'}</strong>
          {authRecoveryAttempts > 0 && ` ⚠️ ${authRecoveryAttempts} ATTEMPTS`}
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={forcePaymentFormRefresh}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🔧 Refresh Form
        </button>
        <button
          onClick={clearPOSProtection}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🧹 Clear Protection
        </button>
        <button
          onClick={testAuthRecoveryProtection}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🧪 Test Protection
        </button>
        <button
          onClick={resetCounters}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginBottom: '5px'
          }}
        >
          🔄 Reset
        </button>
      </div>

      {/* Debug Information */}
      <div style={{ fontSize: '10px' }}>
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#87CEEB' }}>POS Context & Flow</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.posContext, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Payment Form Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.paymentForm, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Payment Processing Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.paymentProcessing, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Protection Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.protection, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Square SDK Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.square, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Auth Recovery Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.authRecovery, null, 2)}
          </div>
        </details>
      </div>

      <div style={{
        marginTop: '15px',
        paddingTop: '10px',
        borderTop: '1px solid #444',
        fontSize: '9px',
        color: '#888'
      }}>
        Last updated: {new Date(debugInfo.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default POSProductionDebugger;
