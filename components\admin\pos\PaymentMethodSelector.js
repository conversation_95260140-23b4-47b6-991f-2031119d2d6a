import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * PaymentMethodSelector component for choosing between cash, card, and hardware payments
 *
 * @param {Object} props - Component props
 * @param {Function} props.onPaymentMethodSelect - Callback when payment method is selected
 * @param {number} props.amount - Total amount to be paid
 * @param {boolean} props.isLoading - Loading state
 * @returns {JSX.Element}
 */
export default function PaymentMethodSelector({ onPaymentMethodSelect, amount, isLoading = false }) {
  const [selectedMethod, setSelectedMethod] = useState(null)
  const [terminalDevices, setTerminalDevices] = useState([])
  const [loadingDevices, setLoadingDevices] = useState(false)

  // Load available Terminal devices on component mount
  useEffect(() => {
    loadTerminalDevices()
  }, [])

  const loadTerminalDevices = async () => {
    setLoadingDevices(true)
    try {
      const response = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTerminalDevices(data.devices || [])
      }
    } catch (error) {
      console.error('Error loading terminal devices:', error)
    } finally {
      setLoadingDevices(false)
    }
  }

  const paymentMethods = [
    {
      id: 'square_terminal',
      name: 'Square Terminal',
      description: 'Hardware card reader with contactless payments',
      icon: '📱',
      color: '#006AFF',
      available: terminalDevices.length > 0,
      requiresDevice: true,
      features: [
        'Contactless & chip payments',
        'Built-in receipt printer',
        'PCI compliant hardware',
        'Real-time processing'
      ]
    },
    {
      id: 'card',
      name: 'Manual Card Entry',
      description: 'Manual card entry through Square Web SDK',
      icon: '💳',
      color: '#4ECDC4',
      available: true,
      features: [
        'Secure card processing',
        'Automatic receipt',
        'Real-time verification',
        'No hardware required'
      ]
    },
    {
      id: 'cash',
      name: 'Cash Payment',
      description: 'Customer pays with cash - record transaction manually',
      icon: '💵',
      color: '#28a745',
      available: true,
      features: [
        'Immediate transaction',
        'No processing fees',
        'Manual receipt generation',
        'No internet required'
      ]
    }
  ]

  const getDeviceStatusIcon = (device) => {
    switch (device.status) {
      case 'PAIRED': return '🟢'
      case 'UNPAIRED': return '🔴'
      case 'UNKNOWN': return '🟡'
      default: return '⚪'
    }
  }

  const handleMethodSelect = (method) => {
    setSelectedMethod(method.id)
    onPaymentMethodSelect(method.id, method)
  }

  return (
    <div className={styles.paymentMethodSelector}>
      <div className={styles.paymentHeader}>
        <h3>Select Payment Method</h3>
        <div className={styles.totalAmount}>
          Total: <span className={styles.amount}>${parseFloat(amount || 0).toFixed(2)}</span>
        </div>
        {loadingDevices && (
          <div className={styles.loadingIndicator}>
            <span>Loading devices...</span>
          </div>
        )}
      </div>

      <div className={styles.paymentMethods}>
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`${styles.paymentMethod} ${selectedMethod === method.id ? styles.selected : ''} ${!method.available ? styles.disabled : ''}`}
            onClick={() => method.available && handleMethodSelect(method)}
            style={{ '--method-color': method.color }}
          >
            <div className={styles.methodIcon}>
              {method.icon}
            </div>
            
            <div className={styles.methodContent}>
              <h4 className={styles.methodName}>
                {method.name}
              </h4>
              
              <p className={styles.methodDescription}>
                {method.description}
              </p>
              
              <ul className={styles.methodFeatures}>
                {method.features.map((feature, index) => (
                  <li key={index} className={styles.feature}>
                    ✓ {feature}
                  </li>
                ))}
              </ul>

              {method.requiresDevice && (
                <div className={styles.deviceInfo}>
                  {terminalDevices.length === 0 ? (
                    <div className={styles.noDevices}>
                      <span className={styles.deviceIcon}>⚠️</span>
                      <span>No devices paired</span>
                    </div>
                  ) : (
                    <div className={styles.deviceList}>
                      <span className={styles.deviceLabel}>Available devices:</span>
                      {terminalDevices.slice(0, 2).map((device) => (
                        <div key={device.id} className={styles.deviceItem}>
                          <span className={styles.deviceStatus}>
                            {getDeviceStatusIcon(device)}
                          </span>
                          <span className={styles.deviceName}>
                            {device.name || `Terminal ${device.id.slice(-4)}`}
                          </span>
                        </div>
                      ))}
                      {terminalDevices.length > 2 && (
                        <span className={styles.moreDevices}>
                          +{terminalDevices.length - 2} more
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}

              {!method.available && (
                <div className={styles.unavailableReason}>
                  {method.requiresDevice && terminalDevices.length === 0
                    ? 'No hardware devices available'
                    : 'Coming Soon'
                  }
                </div>
              )}
            </div>

            <div className={styles.methodSelector}>
              <div className={`${styles.radioButton} ${selectedMethod === method.id ? styles.selected : ''}`}>
                {selectedMethod === method.id && <div className={styles.radioInner}></div>}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedMethod === 'square_terminal' && (
        <div className={styles.terminalInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>📱</span>
            <h4>Square Terminal Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Ensure Square Terminal is powered on and connected</li>
            <li>Customer will be prompted on the Terminal device</li>
            <li>Support for chip, contactless, and mobile payments</li>
            <li>Receipt will print automatically from Terminal</li>
          </ul>
          {terminalDevices.length > 0 && (
            <div className={styles.terminalDeviceStatus}>
              <h5>Connected Devices:</h5>
              {terminalDevices.map((device) => (
                <div key={device.id} className={styles.deviceStatusItem}>
                  {getDeviceStatusIcon(device)} {device.name || `Terminal ${device.id.slice(-4)}`}
                  <span className={styles.deviceStatusText}>({device.status})</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {selectedMethod === 'cash' && (
        <div className={styles.cashInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>💵</span>
            <h4>Cash Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Collect ${parseFloat(amount || 0).toFixed(2)} from the customer</li>
            <li>Provide change if necessary</li>
            <li>The transaction will be recorded as completed</li>
            <li>A receipt will be generated for the customer</li>
          </ul>
        </div>
      )}

      {selectedMethod === 'card' && (
        <div className={styles.cardInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>💳</span>
            <h4>Manual Card Entry Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Customer card details will be entered manually</li>
            <li>Secure processing through Square Web SDK</li>
            <li>Payment will be processed in real-time</li>
            <li>Digital receipt will be sent automatically</li>
          </ul>
        </div>
      )}

      <div className={styles.securityNote}>
        <div className={styles.securityIcon}>🔒</div>
        <div className={styles.securityText}>
          <strong>Secure Processing:</strong> All transactions are encrypted and comply with 
          PCI DSS standards. Customer payment information is never stored locally.
        </div>
      </div>
    </div>
  )
}
