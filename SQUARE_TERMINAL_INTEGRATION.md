# Square Terminal Hardware Integration for Ocean Soul Sparkles POS

## Overview

This implementation provides comprehensive Square Terminal hardware integration for the Ocean Soul Sparkles POS Terminal, enabling automated card processing through Square Terminal devices while maintaining the existing booking workflow.

## Features Implemented

### 1. Square Terminal API Integration ✅
- **Device Pairing**: Create device codes and manage Terminal pairing status
- **Checkout Processing**: Initiate payments on paired Terminal devices
- **Status Monitoring**: Real-time checkout status tracking and updates
- **Webhook Handling**: Process Square Terminal API webhook events

### 2. Hardware Payment Methods ✅
- **Square Terminal**: Hardware card reader with contactless payments
- **Manual Card Entry**: Existing Square Web SDK integration (fallback)
- **Cash Payments**: Manual cash transaction recording

### 3. Device Management ✅
- **Device Discovery**: Automatic detection of paired Terminal devices
- **Status Indicators**: Real-time device status (Paired/Unpaired/Unknown)
- **Device Selection**: User-friendly device selection interface
- **Pairing Instructions**: Step-by-step device pairing guidance

### 4. Enhanced POS Workflow ✅
- **Seamless Integration**: Terminal payments integrate with existing booking flow
- **Error Handling**: Comprehensive error handling and user feedback
- **Status Monitoring**: Real-time payment processing status updates
- **Debugging Tools**: Enhanced POS Production Debugger with Terminal status

## File Structure

### API Endpoints
```
pages/api/admin/pos/
├── terminal-devices.js      # Device management (GET/POST)
├── terminal-checkout.js     # Checkout creation and status (POST/GET)
└── process-payment.js       # Enhanced with Terminal support

pages/api/webhooks/
└── square-terminal.js       # Terminal webhook event handler
```

### Components
```
components/admin/pos/
├── PaymentMethodSelector.js    # Enhanced with Terminal option
├── POSSquareTerminal.js       # Terminal payment component
├── TerminalDeviceManager.js   # Device management interface
├── POSCheckout.js            # Enhanced checkout workflow
└── POSProductionDebugger.js  # Enhanced debugging tools
```

### Database
```
db/migrations/
└── add_terminal_tables.sql   # Terminal-related database tables
```

## Configuration

### Environment Variables
```env
# Square Terminal Configuration
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_app_id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your_location_id
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_ENVIRONMENT=sandbox  # or 'production'
SQUARE_WEBHOOK_SIGNATURE_KEY=your_webhook_signature_key
```

### Database Tables
- `terminal_checkouts`: Track Terminal checkout requests
- `terminal_refunds`: Track Terminal refund requests  
- `terminal_checkout_updates`: Real-time status updates
- Enhanced `payments` table with Terminal fields

## Usage Workflow

### 1. Device Pairing
1. Admin creates device code via Terminal Device Manager
2. Staff enters pairing code on Square Terminal device
3. Device appears as "Paired" in POS Terminal interface

### 2. Payment Processing
1. Customer selects service, artist, tier, and time slot
2. Staff enters customer information
3. Staff selects "Square Terminal" payment method
4. Staff selects paired Terminal device
5. Customer completes payment on Terminal device
6. Payment status updates in real-time
7. Booking and payment records created automatically

### 3. Status Monitoring
- Real-time device status indicators
- Payment processing status updates
- Enhanced debugging information
- Webhook-driven status synchronization

## API Reference

### Device Management
```javascript
// Get paired devices
GET /api/admin/pos/terminal-devices
Authorization: Bearer {adminToken}

// Create device code
POST /api/admin/pos/terminal-devices
{
  "deviceName": "Front Counter Terminal"
}
```

### Terminal Checkout
```javascript
// Create checkout
POST /api/admin/pos/terminal-checkout
{
  "deviceId": "device_id_from_pairing",
  "amount": 50.00,
  "currency": "AUD",
  "orderDetails": {
    "service": "Hair Braiding",
    "customer": "Customer Name"
  }
}

// Get checkout status
GET /api/admin/pos/terminal-checkout?checkoutId={checkout_id}
```

## Security Features

### Authentication
- Admin token required for all Terminal API endpoints
- Supabase authentication integration
- Role-based access control

### Webhook Security
- Signature verification for webhook events
- Request validation and sanitization
- Secure webhook endpoint configuration

### Data Protection
- PCI DSS compliant payment processing
- No sensitive card data stored locally
- Encrypted API communications

## Error Handling

### Payment Errors
- Specific decline reason display
- Network error detection and recovery
- Device connectivity monitoring
- Automatic retry mechanisms

### Device Issues
- Device pairing status monitoring
- Connection failure detection
- Clear error messaging for staff
- Fallback to manual card entry

## Testing

### Sandbox Testing
- Use Square Sandbox environment for development
- Test device codes and checkout flows
- Webhook event simulation
- Error scenario testing

### Production Deployment
- Update environment variables for production
- Configure webhook endpoints
- Test with live Square Terminal devices
- Monitor payment processing logs

## Monitoring and Debugging

### POS Production Debugger
- Terminal device status monitoring
- Payment processing state tracking
- Real-time error detection
- Context-aware status indicators

### Logging
- Comprehensive request/response logging
- Error tracking and reporting
- Performance monitoring
- Webhook event logging

## Maintenance

### Database Cleanup
- Automatic cleanup of old checkout updates
- Payment record archival
- Performance optimization
- Regular maintenance tasks

### Device Management
- Regular device status checks
- Pairing code expiration handling
- Device health monitoring
- Firmware update coordination

## Support and Troubleshooting

### Common Issues
1. **Device Not Pairing**: Check device code expiration and network connectivity
2. **Payment Failures**: Verify Square API credentials and device status
3. **Webhook Issues**: Check webhook signature configuration and endpoint accessibility
4. **Status Updates**: Ensure real-time subscriptions are properly configured

### Debug Tools
- POS Production Debugger for real-time status
- Browser console for detailed error messages
- API endpoint testing for connectivity verification
- Database queries for payment tracking

## Future Enhancements

### Phase 2 Features (Planned)
- Square Reader/Stand integration
- Mobile payment SDK support
- Advanced tip collection
- Receipt customization

### Phase 3 Features (Planned)
- Multi-location device management
- Advanced analytics and reporting
- Inventory integration
- Customer loyalty program integration

## Compliance

### PCI DSS
- Hardware-based card processing
- No card data storage
- Secure API communications
- Regular security assessments

### Australian Regulations
- AUD currency support
- Local payment processing
- Tax calculation integration
- Receipt requirements compliance
